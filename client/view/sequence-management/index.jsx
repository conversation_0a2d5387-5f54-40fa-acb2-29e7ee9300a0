import React, { useState, useEffect } from "react";
import "./index.less";
import { Button, Form, Input, Table, Modal, message } from "antd";
import { queryGroupList, addOrUpdateGroup } from "client/apis/cadre-portrait";

function index(props) {
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [group_name, setGroupName] = useState("");
  const [loading, setLoading] = useState(false);
  const [sequence, setSequence] = useState({
    group_name: "",
    group_id: undefined,
  });

  useEffect(() => {
    loadData(1);
  }, []);

  const onCancel = () => {
    setVisible(false);
  };

  const loadData = async () => {
    setLoading(true);
    const res = await queryGroupList({
      group_name,
    });

    if (res.data.code === 0) {
      setDataSource(res.data.data);
    }

    setLoading(false);
  };

  const columns = [
    {
      title: "序列名称",
      dataIndex: "group_name",
      key: "group_name",
      align: "center",
    },
    {
      title: "成员数",
      dataIndex: "num",
      key: "num",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (text, record) => (
        <div className="operation">
          <a
            className="editor"
            onClick={() => {
              setVisible(true);

              setSequence(record);
            }}
          >
            修改名称
          </a>
          <a
            onClick={() => {
              props.history.push(
                `/sequence-detail?group_id=${record.group_id}`
              );
            }}
          >
            成员名单
          </a>
        </div>
      ),
    },
  ];

  // 生成假数据
  const data = [];
  for (let i = 0; i < 10; i++) {
    data.push({
      key: i,
      name: `John Brown ${i}`,
      age: 32,
      address: `New York No. ${i} Lake Park`,
      count: 32,
    });
  }

  const onOk = async () => {
    const res = await addOrUpdateGroup(sequence);
    console.log("🚀 ~ onOk ~ res:", res);

    if (res.data.code === 0) {
      message.success("创建成功");

      loadData(1);

      setVisible(false);
    }
  };

  return (
    <div className="sequence-management">
      <div className="top-box">
        <div className="sm-t-left">
          <Form.Item
            label="序列名称"
            colon
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Input
              onChange={(e) => {
                setGroupName(e.target.value.trim());
              }}
            />
          </Form.Item>
          <Button
            type="primary"
            onClick={() => {
              loadData(1);
            }}
          >
            查询
          </Button>
        </div>
        <div className="sm-r-right">
          <div
            className="label"
            onClick={() => {
              props.history.push(`/sequence-detail`);
            }}
          >
            未归属序列的人员
          </div>
          <div className="button">
            <Button
              type="primary"
              onClick={() => {
                setVisible(true);
              }}
            >
              创建序列
            </Button>
          </div>
        </div>
      </div>
      <div className="table-box">
        <Table
          dataSource={dataSource}
          columns={columns}
          bordered
          loading={loading}
        />
      </div>
      <Modal
        visible={visible}
        title="创建序列"
        onCancel={() => {
          setVisible(false);
          setSequence({});
        }}
        onOk={onOk}
        destroyOnClose
      >
        <div className="sequence-management-modal-content">
          <Form.Item
            label="序列名称"
            colon
            labelCol={{
              span: 4,
            }}
            wrapperCol={{ span: 20 }}
          >
            <Input
              value={sequence.group_name}
              onChange={(e) => {
                const value = e.target.value;

                setSequence((val) => {
                  val.group_name = value;

                  return { ...val };
                });
              }}
            />
          </Form.Item>
        </div>
      </Modal>
    </div>
  );
}

export default index;
