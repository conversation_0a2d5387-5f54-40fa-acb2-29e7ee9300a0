.cadre-management {
  width: 100%;
  height: 500px; // 固定弹窗内容高度
  display: flex;

  .ant-select-selection {
    width: 100%;
  }

  .left {
    display: flex;
    width: 320px; // 增加宽度以容纳更长的组织名称
    height: 100%; // 左侧区域占满容器高度
    background: #fff;
    flex-direction: column;

    .left-header {
      width: 100%;
      height: 50px;
      background: #f1f5f8;

      text h2 {
        height: 50px;
        line-height: 50px;
        background: #fff;
        width: 100px;
        text-align: center;
      }

      .ant-tabs {
        height: 40px;
      }

      .ant-tabs-tab {
        margin: 0;
        height: 40px;
        line-height: 38px;
        padding: 0 16px;
        border: 1px solid #e8e8e8;
        border-radius: 4px 4px 0 0;
        margin-right: 2px;
      }

      .ant-tabs-tab-active {
        background: #fff;
      }

      .ant-tabs-ink-bar {
        display: none !important;
      }
    }

    .searchOrg {
      margin: 20px;
      // width: 360px;
      background: #f7f8f9;
    }

    .orgContainer {
      position: relative;
      overflow: auto;
      background: #f7f8f9;
      height: calc(100% - 40px); // 减去搜索框和间距的高度
      flex: 1; // 占据剩余空间
    }

    // 为组织树的直接容器设置高度
    > div:last-child {
      height: calc(100% - 40px); // 减去搜索框和间距
      overflow: auto;
      background: #f7f8f9;
      margin-top: 10px;
    }

    // Tree 组件样式优化
    .ant-tree {
      background: transparent;
      padding: 8px; // 为整个树添加内边距

      // 基础树节点样式
      .ant-tree-treenode {
        padding: 2px 0; // 增加节点间距
        margin-bottom: 2px; // 添加节点之间的间距

        // 选中状态样式
        &.ant-tree-treenode-selected > .ant-tree-node-content-wrapper {
          background-color: #e6f7ff !important;
          border: 1px solid #91d5ff;
        }
      }

      .ant-tree-node-content-wrapper {
        width: calc(100% - 24px); // 调整宽度计算，为展开图标留出合适空间
        min-height: 32px; // 设置最小高度
        display: flex;
        align-items: center; // 垂直居中对齐
        padding: 4px 8px; // 增加内边距
        border-radius: 4px; // 添加圆角
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }

        .ant-tree-title {
          width: 100%;
          white-space: normal; // 允许换行
          word-wrap: break-word; // 长单词换行
          word-break: break-all; // 强制换行
          line-height: 1.4; // 设置行高
          padding: 2px 4px; // 调整内边距
          display: flex; // 使用flex布局支持垂直居中
          align-items: center; // 垂直居中对齐
          font-size: 14px; // 设置合适的字体大小
          overflow-wrap: break-word; // 确保长单词换行
          min-height: 24px; // 设置最小高度确保对齐

          // 可点击的树节点标题样式
          .clickable-tree-title {
            width: 100%;
            padding: 4px 6px;
            border-radius: 4px;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: #e6f7ff;
              color: #1890ff;
            }

            &:active {
              background-color: #bae7ff;
            }
          }

          // 普通树节点标题样式
          .tree-title {
            width: 100%;
            padding: 4px 6px;
          }
        }
      }
      .ant-tree-child-tree > li:last-child {
        padding-bottom: 10px !important;
      }

      // 复选框样式调整
      .ant-tree-checkbox {
        margin-right: 8px;
        align-self: center; // 垂直居中对齐
        flex-shrink: 0; // 防止复选框被压缩
        display: flex;
        align-items: center;

        .ant-tree-checkbox-inner {
          vertical-align: middle;
          margin: 0;
        }
      }

      // 展开/收起图标样式
      .ant-tree-switcher {
        width: 24px;
        height: 24px;
        line-height: 24px;
        display: flex;
        align-items: center; // 图标垂直居中
        justify-content: center; // 图标水平居中
        align-self: center; // 确保图标容器垂直居中
        flex-shrink: 0; // 防止压缩
      }

      // 确保所有树节点元素垂直居中对齐
      li.ant-tree-treenode {
        display: flex !important;
        align-items: center !important; // 垂直居中对齐
        min-height: 32px !important;
        flex-wrap: nowrap !important; // 防止换行导致层级混乱
        width: 100% !important; // 确保占满宽度

        // 展开按钮
        > .ant-tree-switcher {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          width: 24px !important;
          height: 24px !important;
          flex-shrink: 0 !important;
          align-self: center !important; // 确保图标垂直居中
        }

        // 内容区域
        > .ant-tree-node-content-wrapper {
          display: flex !important;
          align-items: center !important; // 垂直居中对齐
          flex: 1 !important;

          // 复选框区域
          > .ant-tree-checkbox-wrapper {
            display: flex !important;
            align-items: center !important; // 垂直居中对齐
            margin-right: 8px !important;
            flex-shrink: 0 !important;

            .ant-tree-checkbox {
              margin: 0 !important;
              display: flex !important;
              align-items: center !important;
              height: 16px !important;
            }
          }

          // 文本标题区域
          > .ant-tree-title {
            display: flex !important; // 使用flex布局支持垂直居中
            align-items: center !important; // 垂直居中对齐
            flex: 1 !important;
            line-height: 1.4 !important;
            font-size: 14px !important;
            margin: 0 !important;
            padding: 2px 4px !important;
            word-wrap: break-word !important;
            word-break: break-all !important;
            white-space: normal !important;
            overflow-wrap: break-word !important;
            min-height: 24px !important; // 设置最小高度确保对齐
          }
        }
      }

      // 确保子树的正确层级显示
      .ant-tree-child-tree {
        width: 100% !important;
        margin-left: 0 !important;
        padding-left: 24px !important; // 子节点缩进

        li.ant-tree-treenode {
          display: flex !important;
          align-items: center !important; // 子节点也垂直居中对齐
          width: 100% !important;
          flex-wrap: nowrap !important;
        }
      }

      // 多级嵌套的缩进
      .ant-tree-child-tree .ant-tree-child-tree {
        padding-left: 24px !important; // 每级增加24px缩进
      }

      .ant-tree-child-tree .ant-tree-child-tree .ant-tree-child-tree {
        padding-left: 24px !important;
      }

      // 全局确保垂直居中对齐
      .ant-tree-treenode {
        .ant-tree-node-content-wrapper {
          .ant-tree-checkbox-wrapper {
            display: flex !important;
            align-items: center !important;

            .ant-tree-checkbox {
              display: flex !important;
              align-items: center !important;

              .ant-tree-checkbox-inner {
                vertical-align: middle !important;
              }
            }
          }

          .ant-tree-title {
            display: flex !important;
            align-items: center !important;
          }
        }

        .ant-tree-switcher {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }
      }
    }
  }

  .right {
    flex: 1;
    margin-left: 10px;
    background: #fff;
    padding-bottom: 20px;
    position: relative;
    padding: 20px;

    .cadre-header {
      .header-search {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 20px;
        gap: 10px 10px;

        .header-item {
          .h-i-name {
            white-space: nowrap;
          }
        }

        div {
          display: flex;
          align-items: center;
        }

        .search-span {
          height: 100%;
          padding: 0px 10px;
          background: #fff;
          border: 1px solid #cdcdcd;
          margin-right: 5px;
          white-space: nowrap;
          cursor: pointer;
        }

        .search-active {
          height: 100%;
          padding: 0px 10px;
          border: 1px solid #cdcdcd;
          margin-right: 5px;
          cursor: pointer;
          color: #fff;
          background: #a4adb3;
          white-space: nowrap;
        }

        .search-btn {
          div {
            width: 80px;
            height: 30px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #1fa1fd;
            color: #fff;
            cursor: pointer;
          }

          .reset {
            color: #828a96;
            background: #f7f8fa;
          }
        }
      }

      .header-btn {
        width: 80px;
        height: 30px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #1fa1fd;
        color: #fff;
        cursor: pointer;

        i {
          margin-right: 10px;
        }
      }

      .ant-input,
      .ant-select-selection {
        border-radius: 6px;
      }
    }

    .cadre-content {
      margin-top: 10px;

      .handle {
        display: flex;
        justify-content: space-evenly;
      }
    }
  }

  .button-box {
    display: flex;
    gap: 0px 30px;
  }

  .right-box {
    flex: 1; // 改为弹性布局，自动占据剩余空间
    margin-left: 20px; // 减少左边距
    background: #f7f8f9;
    padding-bottom: 20px;
    position: relative;
    padding: 20px;
    height: 100%; // 右侧区域占满容器高度
    display: flex;
    flex-direction: column;
    min-width: 400px; // 设置最小宽度

    .right-box-header {
      font-size: 16px;
      color: #1fa1fd;

      > span:first-of-type {
        display: inline-block;
        margin-right: 10px;
      }
    }

    .right-box-content {
      margin-top: 25px;
      flex: 1; // 占据剩余空间
      overflow: auto;
      .right-box-item {
        height: auto;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        margin-bottom: 10px;

        .right-box-item-left {
          font-size: 14px;
          flex: 1; // 改为弹性布局，自动占据剩余空间
          color: #000;
          word-wrap: break-word; // 长文本换行
          word-break: break-all; // 强制换行
          line-height: 1.4; // 设置行高

          > div:first-of-type {
            margin-bottom: 5px;
          }
        }

        .icon {
          font-size: 12px;
          color: white;
          cursor: pointer;
          width: 14px;
          height: 14px;
          padding-bottom: 3px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
        }
      }
    }
  }
}

.dr-modal-box {
  .custom-box {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .job-type {
      width: 300px;

      .ant-row {
        margin-bottom: 0px !important;
      }
    }
  }
}
.footer-btn {
  margin-top: 10px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
