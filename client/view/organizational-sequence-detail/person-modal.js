import {
  Button,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Tree,
} from "antd";
import {
  batchAddGroupOrg,
  checkOrgGroup,
  orgDataTree,
} from "client/apis/cadre-portrait";
import { connect } from "dva";
import PropTypes from "prop-types";
import { Component } from "react";

import "./preson.less";
const { confirm } = Modal;
const { Search } = Input;
const { TreeNode } = Tree;

class LeaderGroup extends Component {
  constructor(props) {
    super(props);
    this.state = {
      orgData: [],
      treeData: [],
      expandedKeys: [],
      autoExpandParent: true,
      checkedKeys: [],
      selectedKeys: [],
      searchValue: '',
    };
    // 绑定方法
    this.loop = this.loop.bind(this);
    this.onExpand = this.onExpand.bind(this);
    this.onCheck = this.onCheck.bind(this);
    this.onSelect = this.onSelect.bind(this);
    this.getTreeData = this.getTreeData.bind(this);
    this.removeItem = this.removeItem.bind(this);
    this.submitData = this.submitData.bind(this);
    this.onChange = this.onChange.bind(this);
  }
  componentDidMount() {
    this.getTreeData()
  }
  buildTreeData(items) {
    return items.map((item) => ({
      title: item.name,
      key: item.organization_id,
      children: item.children ? this.buildTreeData(item.children) : [],
    }));
  };

  // 递归获取所有节点的key，用于默认展开
  getAllTreeKeys(treeData) {
    let keys = [];
    const traverse = (nodes) => {
      nodes.forEach(node => {
        keys.push(node.key);
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    traverse(treeData);
    return keys;
  }
  async getTreeData() {
    const res = {
      "code": 0,
      "timestamp": 1751549563025,
      "message": "success",
      "data": [
        {
          "organization_id": 1,
          "name": "攀枝花市东区",
          "short_name": "攀枝花市东区",
          "children": [
            {
              "organization_id": 410,
              "name": "中国共产党攀枝花市东区委员会",
              "short_name": "区委",
              "children": []
            },
            {
              "organization_id": 411,
              "name": "攀枝花市东区人民代表大会常务委员会",
              "short_name": "区人大",
              "children": [
                {
                  "organization_id": 427,
                  "name": "攀枝花市东区人民代表大会常务委员会办公室",
                  "short_name": "区人大办公室",
                  "children": []
                },
                {
                  "organization_id": 428,
                  "name": "攀枝花市东区人民代表大会常务委员会法制工作委员会",
                  "short_name": "法制工作委员会",
                  "children": []
                },
                {
                  "organization_id": 429,
                  "name": "攀枝花市东区人民代表大会常务委员会预算工作委员会",
                  "short_name": "预算工作委员会",
                  "children": []
                },
                {
                  "organization_id": 430,
                  "name": "攀枝花市东区人民代表大会常务委员会代表工作委员会",
                  "short_name": "代表工作委员会",
                  "children": []
                },
                {
                  "organization_id": 431,
                  "name": "攀枝花市东区人民代表大会社会建设委员会",
                  "short_name": "社会建设委员会",
                  "children": []
                },
                {
                  "organization_id": 432,
                  "name": "攀枝花市东区人民代表大会教育科学文化卫生委员会",
                  "short_name": "教育科学文化卫生委员会",
                  "children": []
                },
                {
                  "organization_id": 433,
                  "name": "攀枝花市东区人民代表大会城乡建设环境资源保护委员会",
                  "short_name": "城乡建设环境资源保护委员会",
                  "children": []
                },
                {
                  "organization_id": 434,
                  "name": "攀枝花市东区人民代表大会财政经济委员会",
                  "short_name": "财政经济委员会",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 412,
              "name": "攀枝花市东区人民政府",
              "short_name": "区政府",
              "children": []
            },
            {
              "organization_id": 413,
              "name": "中国人民政治协商会议攀枝花市东区委员会",
              "short_name": "区政协",
              "children": [
                {
                  "organization_id": 435,
                  "name": "中国人民政治协商会议攀枝花市东区委员会办公室",
                  "short_name": "区政协办公室",
                  "children": []
                },
                {
                  "organization_id": 436,
                  "name": "中国人民政治协商会议攀枝花市东区委员会提案委员会",
                  "short_name": "提案委员会",
                  "children": []
                },
                {
                  "organization_id": 437,
                  "name": "中国人民政治协商会议攀枝花市东区委员会文化文史和学习委员会",
                  "short_name": "文化文史和学习委员会",
                  "children": []
                },
                {
                  "organization_id": 438,
                  "name": "中国人民政治协商会议攀枝花市东区委员会人口资源环境委员会",
                  "short_name": "人口资源环境委员会",
                  "children": []
                },
                {
                  "organization_id": 439,
                  "name": "中国人民政治协商会议攀枝花市东区委员会委员联络委员会",
                  "short_name": "委员联络委员会",
                  "children": []
                },
                {
                  "organization_id": 440,
                  "name": "中国人民政治协商会议攀枝花市东区委员会经济委员会",
                  "short_name": "经济委员会",
                  "children": []
                },
                {
                  "organization_id": 441,
                  "name": "中国人民政治协商会议攀枝花市东区委员会社会法制和民族宗教委员会",
                  "short_name": "社会法制和民族宗教委员会",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 414,
              "name": "攀枝花市东区纪委监委",
              "short_name": "区纪委监委",
              "children": [
                {
                  "organization_id": 442,
                  "name": "攀枝花市东区纪委监委副科长级室主任",
                  "short_name": "区纪委监委室主任",
                  "children": []
                },
                {
                  "organization_id": 443,
                  "name": "攀枝花市东区纪检监察教育培训与信息中心",
                  "short_name": "区纪检监察教育培训与信息中心",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 415,
              "name": "攀枝花市东区人民法院",
              "short_name": "区人民法院",
              "children": [
                {
                  "organization_id": 444,
                  "name": "攀枝花市东区人民法院执行局",
                  "short_name": "区人民法院执行局",
                  "children": []
                },
                {
                  "organization_id": 445,
                  "name": "攀枝花市东区人民法院审判委员会",
                  "short_name": "区人民法院审判委员会",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 416,
              "name": "攀枝花市东区人民检察院",
              "short_name": "区人民检察院",
              "children": [
                {
                  "organization_id": 446,
                  "name": "攀枝花市东区人民检察院检察委员会",
                  "short_name": "区人民检察院检察委员会",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 417,
              "name": "攀枝花市东区区委工作部门",
              "short_name": "区委工作部门",
              "children": [
                {
                  "organization_id": 447,
                  "name": "中共攀枝花市东区委员会办公室",
                  "short_name": "区委办公室",
                  "children": [
                    {
                      "organization_id": 448,
                      "name": "攀枝花市东区电子政务建设服务中心",
                      "short_name": "区电子政务建设服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 449,
                  "name": "中共攀枝花市东区委员会组织部",
                  "short_name": "区委组织部",
                  "children": [
                    {
                      "organization_id": 450,
                      "name": "中共攀枝花市东区委员会党建领导小组办公室",
                      "short_name": "区委党建办",
                      "children": []
                    },
                    {
                      "organization_id": 451,
                      "name": "中共攀枝花市东区委员会组织部党员教育中心",
                      "short_name": "党员教育中心",
                      "children": []
                    },
                    {
                      "organization_id": 452,
                      "name": "中共攀枝花市东区区委党校",
                      "short_name": "区委党校",
                      "children": []
                    },
                    {
                      "organization_id": 453,
                      "name": "攀枝花市东区高端人才服务中心",
                      "short_name": "区高端人才服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 454,
                      "name": "攀枝花市东区党建服务中心",
                      "short_name": "区党建服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 455,
                  "name": "中共攀枝花市东区委员会宣传部",
                  "short_name": "区委宣传部",
                  "children": [
                    {
                      "organization_id": 456,
                      "name": "攀枝花市东区融媒体中心",
                      "short_name": "区融媒体中心",
                      "children": []
                    },
                    {
                      "organization_id": 457,
                      "name": "攀枝花市东区文联",
                      "short_name": "区文联",
                      "children": []
                    },
                    {
                      "organization_id": 458,
                      "name": "攀枝花市东区舆情信息中心",
                      "short_name": "区舆情信息中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 459,
                  "name": "中共攀枝花市东区委员会统一战线工作部",
                  "short_name": "区委统战部",
                  "children": [
                    {
                      "organization_id": 460,
                      "name": "攀枝花市东区民族宗教事务局",
                      "short_name": "区民宗局",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 461,
                  "name": "中共攀枝花市东区委员会社会工作部",
                  "short_name": "区委社工部",
                  "children": []
                },
                {
                  "organization_id": 462,
                  "name": "中共攀枝花市东区委员会政法委员会",
                  "short_name": "区委政法委",
                  "children": [
                    {
                      "organization_id": 463,
                      "name": "攀枝花市东区社会治安综合治理中心",
                      "short_name": "区综治中心",
                      "children": []
                    },
                    {
                      "organization_id": 464,
                      "name": "攀枝花市东区智慧联动中心",
                      "short_name": "区智慧联动中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 465,
                  "name": "中共攀枝花市东区区委机构编制委员会办公室",
                  "short_name": "区委编办",
                  "children": []
                },
                {
                  "organization_id": 466,
                  "name": "中共攀枝花市东区区委巡察工作领导小组办公室",
                  "short_name": "区委巡察办",
                  "children": [
                    {
                      "organization_id": 467,
                      "name": "中共攀枝花市东区区区委巡察组",
                      "short_name": "区委巡察组",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 468,
                  "name": "中共攀枝花市东区区委共同富裕试验区建设办公室",
                  "short_name": "区委共富办",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 418,
              "name": "攀枝花市东区政府工作部门",
              "short_name": "区政府工作部门",
              "children": [
                {
                  "organization_id": 469,
                  "name": "攀枝花市东区人民政府办公室",
                  "short_name": "区政府办公室",
                  "children": [
                    {
                      "organization_id": 470,
                      "name": "攀枝花市东区机关效能建设事务中心",
                      "short_name": "区机关效能建设事务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 471,
                  "name": "攀枝花市东区发展和改革局",
                  "short_name": "区发改局",
                  "children": [
                    {
                      "organization_id": 472,
                      "name": "攀枝花市东区重大项目推进服务中心",
                      "short_name": "区重大项目推进服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 473,
                      "name": "攀枝花市东区大数据中心",
                      "short_name": "区大数据中心",
                      "children": []
                    },
                    {
                      "organization_id": 474,
                      "name": "攀枝花市东区价格认证中心",
                      "short_name": "区价格认证中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 475,
                  "name": "攀枝花市东区经济和信息化局",
                  "short_name": "区经信局",
                  "children": [
                    {
                      "organization_id": 476,
                      "name": "攀枝花市东区地企协作促进中心",
                      "short_name": "区地企协作促进中心",
                      "children": []
                    },
                    {
                      "organization_id": 477,
                      "name": "攀枝花市东区循环经济服务中心",
                      "short_name": "区循环经济服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 478,
                  "name": "攀枝花市东区教育和体育局",
                  "short_name": "区教体局",
                  "children": [
                    {
                      "organization_id": 479,
                      "name": "攀枝花市东区教育科学研究室",
                      "short_name": "区教育科学研究室",
                      "children": []
                    },
                    {
                      "organization_id": 480,
                      "name": "攀枝花市东区教育信息技术中心",
                      "short_name": "区教育信息技术中心",
                      "children": []
                    },
                    {
                      "organization_id": 481,
                      "name": "攀枝花市东区学校后勤保障中心",
                      "short_name": "区学校后勤保障中心",
                      "children": []
                    },
                    {
                      "organization_id": 482,
                      "name": "攀枝花市东区教育考试中心",
                      "short_name": "区教育考试中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 483,
                  "name": "攀枝花市东区科学技术局",
                  "short_name": "区科技局",
                  "children": [
                    {
                      "organization_id": 484,
                      "name": "攀枝花市东区科学技术协会",
                      "short_name": "区科学技术协会",
                      "children": []
                    },
                    {
                      "organization_id": 485,
                      "name": "攀枝花市东区生产力促进中心",
                      "short_name": "区生产力促进中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 486,
                  "name": "攀枝花市东区民政局",
                  "short_name": "区民政局",
                  "children": []
                },
                {
                  "organization_id": 487,
                  "name": "攀枝花市东区司法局",
                  "short_name": "区司法局",
                  "children": [
                    {
                      "organization_id": 488,
                      "name": "四川省攀枝花市金衡公证处",
                      "short_name": "四川省攀枝花市金衡公证处",
                      "children": []
                    },
                    {
                      "organization_id": 489,
                      "name": "攀枝花市东区法律援助中心",
                      "short_name": "区法律援助中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 490,
                  "name": "攀枝花市东区财政局",
                  "short_name": "区财政局",
                  "children": [
                    {
                      "organization_id": 491,
                      "name": "攀枝花市东区财政国库支付中心",
                      "short_name": "区财政国库支付中心",
                      "children": []
                    },
                    {
                      "organization_id": 492,
                      "name": "攀枝花市东区政府投资评审中心",
                      "short_name": "区政府投资评审中心",
                      "children": []
                    },
                    {
                      "organization_id": 493,
                      "name": "攀枝花市东区国资国企服务中心",
                      "short_name": "区国资国企服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 494,
                  "name": "攀枝花市东区人力资源和社会保障局",
                  "short_name": "区人社局",
                  "children": [
                    {
                      "organization_id": 495,
                      "name": "攀枝花市东区人才服务中心",
                      "short_name": "区人才服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 496,
                      "name": "攀枝花市东区就业创业促进中心",
                      "short_name": "区就业创业促进中心",
                      "children": []
                    },
                    {
                      "organization_id": 497,
                      "name": "攀枝花市东区社会保险事务中心",
                      "short_name": "区社会保险事务中心",
                      "children": []
                    },
                    {
                      "organization_id": 498,
                      "name": "攀枝花市东区劳动人事争议仲裁院",
                      "short_name": "区劳动人事争议仲裁院",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 499,
                  "name": "攀枝花市东区林业局",
                  "short_name": "区林业局",
                  "children": []
                },
                {
                  "organization_id": 500,
                  "name": "攀枝花市东区住房和城乡建设局",
                  "short_name": "区住建局",
                  "children": [
                    {
                      "organization_id": 501,
                      "name": "攀枝花市东区建设项目服务中心",
                      "short_name": "区建设项目服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 502,
                  "name": "攀枝花市东区农业农村和交通水利局",
                  "short_name": "区农交水局",
                  "children": [
                    {
                      "organization_id": 503,
                      "name": "中共攀枝花市东区区委农村工作领导小组办公室",
                      "short_name": "区委农办",
                      "children": []
                    },
                    {
                      "organization_id": 504,
                      "name": "攀枝花市东区公路发展中心",
                      "short_name": "区公路发展中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 505,
                  "name": "攀枝花市东区商务局",
                  "short_name": "区商务局",
                  "children": [
                    {
                      "organization_id": 506,
                      "name": "攀枝花市东区现代服务业促进中心",
                      "short_name": "区现代服务业促进中心",
                      "children": []
                    },
                    {
                      "organization_id": 507,
                      "name": "攀枝花市东区商务服务中心",
                      "short_name": "区商务服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 508,
                  "name": "攀枝花市东区文化广播电视和旅游局",
                  "short_name": "区文广旅局",
                  "children": [
                    {
                      "organization_id": 509,
                      "name": "攀枝花市东区文化馆",
                      "short_name": "区文化馆",
                      "children": []
                    },
                    {
                      "organization_id": 510,
                      "name": "攀枝花市东区旅游促进中心",
                      "short_name": "区旅游促进中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 511,
                  "name": "攀枝花市东区卫生健康局",
                  "short_name": "区卫健局",
                  "children": [
                    {
                      "organization_id": 512,
                      "name": "攀枝花市东区基层卫生发展促进中心",
                      "short_name": "区基层卫生发展促进中心",
                      "children": []
                    },
                    {
                      "organization_id": 513,
                      "name": "攀枝花市东区疾病预防控制中心",
                      "short_name": "区疾病预防控制中心",
                      "children": []
                    },
                    {
                      "organization_id": 514,
                      "name": "攀枝花市东区妇幼保健服务中心",
                      "short_name": "区妇幼保健服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 515,
                  "name": "攀枝花市东区退役军人事务局",
                  "short_name": "区退役军人事务局",
                  "children": [
                    {
                      "organization_id": 516,
                      "name": "攀枝花市东区退役军人服务中心",
                      "short_name": "区退役军人服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 517,
                  "name": "攀枝花市东区应急管理局",
                  "short_name": "区应急管理局",
                  "children": [
                    {
                      "organization_id": 518,
                      "name": "攀枝花市东区应急救援服务中心",
                      "short_name": "区应急救援服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 519,
                      "name": "攀枝花市东区应急物资储备中心",
                      "short_name": "区应急物资储备中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 520,
                  "name": "攀枝花市东区审计局",
                  "short_name": "区审计局",
                  "children": [
                    {
                      "organization_id": 521,
                      "name": "攀枝花市东区经济责任审计服务中心",
                      "short_name": "区经济责任审计服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 522,
                  "name": "攀枝花市东区市场监督管理局",
                  "short_name": "区市场监督管理局",
                  "children": [
                    {
                      "organization_id": 523,
                      "name": "攀枝花市东区消费维权和个私经济指导服务中心",
                      "short_name": "区消费维权和个私经济指导服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 524,
                  "name": "攀枝花市东区统计局",
                  "short_name": "区统计局",
                  "children": [
                    {
                      "organization_id": 525,
                      "name": "攀枝花市东区普查中心",
                      "short_name": "区普查中心",
                      "children": []
                    },
                    {
                      "organization_id": 526,
                      "name": "攀枝花市东区社会经济调查队",
                      "short_name": "区社会经济调查队",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 527,
                  "name": "攀枝花市东区信访局",
                  "short_name": "区信访局",
                  "children": [
                    {
                      "organization_id": 528,
                      "name": "攀枝花市东区群众接待中心",
                      "short_name": "区群众接待中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 529,
                  "name": "攀枝花市东区经济合作局",
                  "short_name": "区经合局",
                  "children": [
                    {
                      "organization_id": 530,
                      "name": "攀枝花市东区投资促进服务中心",
                      "short_name": "区投资促进服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 531,
                  "name": "攀枝花市东区医疗保障局",
                  "short_name": "区医保局",
                  "children": [
                    {
                      "organization_id": 532,
                      "name": "攀枝花市东区医疗保障事务中心",
                      "short_name": "区医疗保障事务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 533,
                  "name": "攀枝花市东区行政审批局",
                  "short_name": "区行政审批局",
                  "children": [
                    {
                      "organization_id": 534,
                      "name": "攀枝花市东区行政审批服务中心",
                      "short_name": "行政审批服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 535,
                      "name": "攀枝花市东区政府采购服务中心（区惠民帮扶中心）",
                      "short_name": "区政府采购服务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 536,
                  "name": "攀枝花市东区综合行政执法局",
                  "short_name": "区综合行政执法局",
                  "children": [
                    {
                      "organization_id": 537,
                      "name": "攀枝花市东区市容环境卫生服务中心",
                      "short_name": "区市容环境卫生服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 538,
                      "name": "攀枝花市东区东城环境卫生服务所",
                      "short_name": "区东城环境卫生服务所",
                      "children": []
                    },
                    {
                      "organization_id": 539,
                      "name": "攀枝花市东区固体废弃物流转站",
                      "short_name": "区固体废弃物流转站",
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "organization_id": 419,
              "name": "攀枝花市东区政府直属事业单位",
              "short_name": "区政府直属事业单位",
              "children": [
                {
                  "organization_id": 540,
                  "name": "攀枝花市东区档案馆",
                  "short_name": "区档案馆",
                  "children": []
                },
                {
                  "organization_id": 541,
                  "name": "攀枝花市东区土地储备中心",
                  "short_name": "区土地储备中心",
                  "children": []
                },
                {
                  "organization_id": 542,
                  "name": "攀枝花市东区机关事务服务中心",
                  "short_name": "区机关事务服务中心",
                  "children": []
                },
                {
                  "organization_id": 543,
                  "name": "中共攀枝花市东区区委党史研究室",
                  "short_name": "区委党史研究室",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 420,
              "name": "攀枝花市东区群团组织",
              "short_name": "群团组织",
              "children": [
                {
                  "organization_id": 544,
                  "name": "攀枝花市东区总工会",
                  "short_name": "区总工会",
                  "children": []
                },
                {
                  "organization_id": 545,
                  "name": "共青团攀枝花市东区委员会",
                  "short_name": "团区委",
                  "children": []
                },
                {
                  "organization_id": 546,
                  "name": "攀枝花市东区妇女联合会",
                  "short_name": "区妇联",
                  "children": []
                },
                {
                  "organization_id": 547,
                  "name": "攀枝花市东区残疾人联合会",
                  "short_name": "区残联",
                  "children": []
                },
                {
                  "organization_id": 548,
                  "name": "攀枝花市东区工商业联合会",
                  "short_name": "区工商联",
                  "children": []
                },
                {
                  "organization_id": 549,
                  "name": "攀枝花市东区红十字会",
                  "short_name": "区红十字会",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 421,
              "name": "攀枝花市东区街道（镇）",
              "short_name": "街道（镇）",
              "children": [
                {
                  "organization_id": 562,
                  "name": "攀枝花市东区大渡口街道办事处",
                  "short_name": "大渡口街道",
                  "children": [
                    {
                      "organization_id": 563,
                      "name": "攀枝花市东区大渡口街道便民服务中心",
                      "short_name": "大渡口街道便民服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 564,
                      "name": "攀枝花市东区大渡口街道综合事务中心",
                      "short_name": "大渡口街道综合事务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 565,
                  "name": "攀枝花市东区炳草岗街道办事处",
                  "short_name": "炳草岗街道",
                  "children": [
                    {
                      "organization_id": 566,
                      "name": "攀枝花市东区炳草岗街道便民服务中心",
                      "short_name": "炳草岗街道便民服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 567,
                      "name": "攀枝花市东区炳草岗街道综合事务中心",
                      "short_name": "炳草岗街道综合事务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 568,
                  "name": "攀枝花市东区东华街道办事处",
                  "short_name": "东华街道",
                  "children": [
                    {
                      "organization_id": 569,
                      "name": "攀枝花市东区东华街道便民服务中心",
                      "short_name": "东华街道便民服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 570,
                      "name": "攀枝花市东区东华街道综合事务中心",
                      "short_name": "东华街道综合事务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 571,
                  "name": "攀枝花市东区弄弄坪街道办事处",
                  "short_name": "弄弄坪街道",
                  "children": [
                    {
                      "organization_id": 572,
                      "name": "攀枝花市东区弄弄坪街道便民服务中心",
                      "short_name": "弄弄坪街道便民服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 573,
                      "name": "攀枝花市东区弄弄坪街道综合事务中心",
                      "short_name": "弄弄坪街道综合事务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 574,
                  "name": "攀枝花市东区瓜子坪街道办事处",
                  "short_name": "瓜子坪街道",
                  "children": [
                    {
                      "organization_id": 575,
                      "name": "攀枝花市东区瓜子坪街道便民服务中心",
                      "short_name": "瓜子坪街道便民服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 576,
                      "name": "攀枝花市东区瓜子坪街道综合事务中心",
                      "short_name": "瓜子坪街道综合事务中心",
                      "children": []
                    }
                  ]
                },
                {
                  "organization_id": 577,
                  "name": "攀枝花市东区银江镇人民政府",
                  "short_name": "银江镇",
                  "children": [
                    {
                      "organization_id": 578,
                      "name": "攀枝花市东区银江镇便民服务中心",
                      "short_name": "银江镇便民服务中心",
                      "children": []
                    },
                    {
                      "organization_id": 579,
                      "name": "攀枝花市东区银江镇农业农村服务中心",
                      "short_name": "银江镇农业农村服务中心",
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "organization_id": 422,
              "name": "攀枝花东区高新技术产业园区",
              "short_name": "园区",
              "children": [
                {
                  "organization_id": 550,
                  "name": "攀枝花东区高新技术产业园区党政办公室（党群工作部）",
                  "short_name": "园区党政办公室",
                  "children": []
                },
                {
                  "organization_id": 551,
                  "name": "攀枝花东区高新技术产业园区规划发展部",
                  "short_name": "园区规划发展部",
                  "children": []
                },
                {
                  "organization_id": 552,
                  "name": "攀枝花东区高新技术产业园区经济运行部（统计部）",
                  "short_name": "园区经济运行部",
                  "children": []
                },
                {
                  "organization_id": 553,
                  "name": "攀枝花东区高新技术产业园区应急管理和生态环境部",
                  "short_name": "园区应急管理和生态环境部",
                  "children": []
                },
                {
                  "organization_id": 554,
                  "name": "攀枝花东区高新技术产业园区科技创新部",
                  "short_name": "园区科技创新部",
                  "children": []
                },
                {
                  "organization_id": 555,
                  "name": "攀枝花市东区园区服务中心",
                  "short_name": "区园区服务中心",
                  "children": []
                },
                {
                  "organization_id": 556,
                  "name": "攀枝花市东区园区生态环境和应急服务中心",
                  "short_name": "区园区生态环境和应急服务中心",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 423,
              "name": "攀枝花市东区属国有企业",
              "short_name": "区属国有企业",
              "children": [
                {
                  "organization_id": 559,
                  "name": "攀枝花市兴东投资建设集团有限责任公司",
                  "short_name": "兴东集团",
                  "children": []
                },
                {
                  "organization_id": 560,
                  "name": "攀枝花市渡口产业发展集团有限公司",
                  "short_name": "渡口集团",
                  "children": []
                },
                {
                  "organization_id": 561,
                  "name": "攀枝花市东区高创投资开发有限责任公司",
                  "short_name": "高创投",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 424,
              "name": "攀枝花市东区人民武装部",
              "short_name": "区人武部",
              "children": [
                {
                  "organization_id": 557,
                  "name": "攀枝花市东区民兵武器装备仓库",
                  "short_name": "区民兵武器装备仓库",
                  "children": []
                },
                {
                  "organization_id": 558,
                  "name": "攀枝花市东区民兵训练基地",
                  "short_name": "区民兵训练基地",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 425,
              "name": "攀枝花市东区辖区内学校",
              "short_name": "学校",
              "children": [
                {
                  "organization_id": 580,
                  "name": "攀枝花市第一小学校",
                  "short_name": "攀枝花市第一小学校",
                  "children": []
                },
                {
                  "organization_id": 581,
                  "name": "攀枝花市第二小学校",
                  "short_name": "攀枝花市第二小学校",
                  "children": []
                },
                {
                  "organization_id": 582,
                  "name": "攀枝花市凤凰小学校",
                  "short_name": "攀枝花市凤凰小学校",
                  "children": []
                },
                {
                  "organization_id": 583,
                  "name": "攀枝花市第五小学校",
                  "short_name": "攀枝花市第五小学校",
                  "children": []
                },
                {
                  "organization_id": 584,
                  "name": "攀枝花市第六小学校",
                  "short_name": "攀枝花市第六小学校",
                  "children": []
                },
                {
                  "organization_id": 585,
                  "name": "攀枝花市第九小学校",
                  "short_name": "攀枝花市第九小学校",
                  "children": []
                },
                {
                  "organization_id": 586,
                  "name": "攀枝花市第十小学校",
                  "short_name": "攀枝花市第十小学校",
                  "children": []
                },
                {
                  "organization_id": 587,
                  "name": "攀枝花市东区银江中心学校",
                  "short_name": "攀枝花市东区银江中心学校",
                  "children": []
                },
                {
                  "organization_id": 588,
                  "name": "攀枝花市第十五中学校",
                  "short_name": "攀枝花市第十五中学校",
                  "children": []
                },
                {
                  "organization_id": 589,
                  "name": "攀枝花市南山实验学校",
                  "short_name": "攀枝花市南山实验学校",
                  "children": []
                },
                {
                  "organization_id": 590,
                  "name": "攀枝花市二十五中小学校",
                  "short_name": "攀枝花市二十五中小学校",
                  "children": []
                },
                {
                  "organization_id": 591,
                  "name": "攀枝花市花城外国语学校",
                  "short_name": "攀枝花市花城外国语学校",
                  "children": []
                },
                {
                  "organization_id": 592,
                  "name": "攀枝花市炳草岗二街坊幼儿园",
                  "short_name": "攀枝花市炳草岗二街坊幼儿园",
                  "children": []
                },
                {
                  "organization_id": 593,
                  "name": "攀枝花市十九中小学校",
                  "short_name": "攀枝花市十九中小学校",
                  "children": []
                }
              ]
            },
            {
              "organization_id": 426,
              "name": "攀枝花市东区辖区内医院",
              "short_name": "医院",
              "children": [
                {
                  "organization_id": 594,
                  "name": "攀枝花市东区炳三区社区卫生服务中心",
                  "short_name": "炳三区社区卫生服务中心",
                  "children": []
                },
                {
                  "organization_id": 595,
                  "name": "攀枝花市东区九附六社区卫生服务中心",
                  "short_name": "九附六社区卫生服务中心",
                  "children": []
                },
                {
                  "organization_id": 596,
                  "name": "攀枝花市东区红星社区卫生服务中心",
                  "short_name": "红星社区卫生服务中心",
                  "children": []
                },
                {
                  "organization_id": 597,
                  "name": "攀枝花市东区瓜子坪社区卫生服务中心",
                  "short_name": "瓜子坪社区卫生服务中心",
                  "children": []
                },
                {
                  "organization_id": 598,
                  "name": "攀枝花市东区大渡口社区卫生服务中心",
                  "short_name": "大渡口社区卫生服务中心",
                  "children": []
                },
                {
                  "organization_id": 599,
                  "name": "攀枝花市东区弄弄坪社区卫生服务中心",
                  "short_name": "弄弄坪社区卫生服务中心",
                  "children": []
                },
                {
                  "organization_id": 600,
                  "name": "攀枝花市东区银江镇卫生院",
                  "short_name": "银江镇卫生院",
                  "children": []
                },
                {
                  "organization_id": 601,
                  "name": "攀枝花市东区妇幼保健服务中心",
                  "short_name": "区妇幼保健服务中心（医院）",
                  "children": []
                },
                {
                  "organization_id": 602,
                  "name": "攀枝花市东区疾病预防控制中心（医院）",
                  "short_name": "区疾病预防控制中心（医院）",
                  "children": []
                }
              ]
            }
          ]
        }
      ],
      "status": 200,
      "time_string": "2025-07-03 13:32:43.043"
    }
    console.log("🚀 ~ res.data:", res.data)
    let newtreeData = this.buildTreeData(res.data);
    // 获取所有节点的key用于默认展开
    let allKeys = this.getAllTreeKeys(newtreeData);
    this.setState({
      treeData: newtreeData,
      expandedKeys: allKeys,
      autoExpandParent: false
    });
    return
    try {
      let res = await orgDataTree();
      if (res.data && res.data.data) {
        let newtreeData = this.buildTreeData(res.data.data);
        // 获取所有节点的key用于默认展开
        let allKeys = this.getAllTreeKeys(newtreeData);
        this.setState({
          treeData: newtreeData,
          expandedKeys: allKeys,
          autoExpandParent: false
        });
      } else {
        console.error('API 返回的数据格式不正确:', res);
      }
    } catch (error) {
      console.error('获取树数据时出错:', error);
      message.error('获取树数据失败，请稍后重试');
    }
  }
  removeItem(index, id) {
    const { orgData, checkedKeys } = this.state;
    const neworgData = [...orgData];
    // 获取要删除的项
    const removedItem = neworgData.splice(index, 1)[0];
    // 过滤 checkedKeys，移除与被删除项对应的 key
    // 检查 checkedKeys 是否为数组，若不是则初始化为空数组
    const validCheckedKeys = Array.isArray(checkedKeys) ? checkedKeys : [];
    let newCheckedKeys = validCheckedKeys;
    if (removedItem) {
      // 过滤 checkedKeys，移除与被删除项对应的 key
      newCheckedKeys = validCheckedKeys.filter(key => key != removedItem.org_id);
    }
    this.setState({
      orgData: neworgData,
      checkedKeys: newCheckedKeys,
      autoExpandParent: true,
    });
  }
  submitData() {
    const { orgData } = this.state;
    let cadres = []
    orgData.map((item, index) => {
      cadres.push({
        org_id: item.org_id,
        group_id: this.props.groupId,
        name: item.name,
      })
    })
    checkOrgGroup({ orgs: cadres }).then((res) => {
      let _this = this;
      if (res.data.code === 0) {
        if (res.data.data.existsOrgIds.length > 0) {

          confirm({
            content: (
              <div>
                <p>
                  {
                    res.data.data.existsOrgNames.map((item, index) => {
                      // 除了最后一项，其余项后面添加逗号
                      return index === res.data.data.existsOrgNames.length - 1 ?
                        <span key={index}>{item}</span> :
                        <span key={index}>{item},</span>;
                    })
                  }
                </p>
                <p>更新：将序列修改为当前序列</p>
                <p>跳过：继续在原序列</p>
              </div>
            ),
            cancelText: '更新',
            okText: '跳过',
            // okType: 'danger',
            icon: <Icon type="exclamation-circle" style={{ color: 'red' }} />,
            onOk() {
              let obj = {
                need_exclude_org_ids: res.data.data.existsOrgIds,
                operate_mark: 2,
                orgs: cadres
              }
              batchAddGroupOrg(obj).then((re) => {
                if (re.data.code === 0) {
                  message.success('添加成功');
                  _this.setState({
                    orgData: [],
                    checkedKeys: [],
                    selectedKeys: []
                  })
                  _this.props.hideModal()
                }
              })
            },
            onCancel() {
              let obj = {
                need_exclude_org_ids: res.data.data.existsOrgIds,
                operate_mark: 1,
                orgs: cadres
              }
              batchAddGroupOrg(obj).then((r) => {
                if (r.data.code === 0) {
                  message.success('添加成功');
                  _this.setState({
                    orgData: [],
                    checkedKeys: [],
                    selectedKeys: []
                  })
                  _this.props.hideModal()
                }
              })
            },

          });
        } else {
          batchAddGroupOrg({ orgs: cadres }).then((res) => {
            if (res.data.code === 0) {
              message.success('添加成功');
              this.setState({
                orgData: [],
                checkedKeys: [],
                selectedKeys: []
              })
              this.props.hideModal()
            }
          })
        }
      } else {
        message.error(res.data.message);
      }
    });
  }
  onExpand(expandedKeys) {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };
  looptree(data, keys) {
    // 检查 keys 数组是否为空
    if (keys.length === 0) {
      return [];
    }
    let arr = [];
    data.forEach(item => {
      // 使用 some 方法检查 item.key 是否存在于 keys 数组中
      if (keys.some(key => item.key == key)) {
        arr.push({
          org_id: item.key,
          name: item.title,
        });
      }
      if (item.children) {
        // 处理递归返回的结果并合并到 arr 中
        arr = arr.concat(this.looptree(item.children, keys));
      }
    });
    return arr;
  }
  onCheck(checkedKeys, info) {
    const { treeData } = this.state;
    let arr = this.looptree(treeData, checkedKeys)
    this.setState({
      checkedKeys,
      orgData: arr
    });
  };

  onSelect(selectedKeys, info) {
    this.setState({ selectedKeys });
  };
  loop(data) {
    const { searchValue } = this.state;
    return data.map(item => {
      const index = item.title.indexOf(searchValue);
      const beforeStr = item.title.substr(0, index);
      const afterStr = item.title.substr(index + searchValue.length);
      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: '#f50' }}>{searchValue}</span>
            {afterStr}
          </span>
        ) : (
          <span>{item.title}</span>
        );
      if (item.children) {
        return (
          <TreeNode key={item.key} title={title}>
            {this.loop(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode key={item.key} title={title} />;
    });
  }
  getParentKey(key, tree) {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key;
        } else if (this.getParentKey(key, node.children)) {
          parentKey = this.getParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };
  onChange(e) {
    const { treeData } = this.state;
    const { value } = e.target;
    const expandedKeysSet = new Set();

    // 定义一个递归函数来查找匹配节点及其父节点
    const findExpandedKeys = (nodes) => {
      nodes.forEach((node) => {
        if (node.title.indexOf(value) > -1) {
          // 添加匹配节点的父节点键
          let parentKey = this.getParentKey(node.key, treeData);
          while (parentKey) {
            expandedKeysSet.add(parentKey);
            parentKey = this.getParentKey(parentKey, treeData);
          }
          // 添加匹配节点自身的键
          expandedKeysSet.add(node.key);
        }
        if (node.children) {
          findExpandedKeys(node.children);
        }
      });
    };

    findExpandedKeys(treeData);
    const expandedKeys = Array.from(expandedKeysSet);
    this.setState({
      expandedKeys,
      searchValue: value,
      autoExpandParent: true,
    });
  };
  render() {
    const { orgData, treeData } = this.state;
    return (
      <Modal
        footer={null}
        title={this.props.title}
        width='800px'
        destroyOnClose={true}
        visible={this.props.visible}
        onCancel={
          () => {
            this.props.hideModal()
            this.setState({
              orgData: [],
              checkedKeys: [],
              selectedKeys: []
            })
          }
        }
      >
        <div className="cadre-management">
          <div className="left">
            <Search style={{ marginBottom: 8 }} placeholder="请输入机构名称" onChange={this.onChange} />
            <div style={{ background: '#f7f8f9', marginTop: '10px' }}>
              {
                treeData.length > 0 &&
                <Tree
                  checkable
                  onExpand={this.onExpand}
                  expandedKeys={this.state.expandedKeys}
                  autoExpandParent={this.state.autoExpandParent}
                  onCheck={this.onCheck}
                  checkedKeys={this.state.checkedKeys}
                  onSelect={this.onSelect}
                  selectedKeys={this.state.selectedKeys}
                >
                  {this.loop(treeData)}
                </Tree>
              }
            </div>

          </div>
          <div className="right-box">
            <div className="right-box-header">
              <span>已选择</span><span>({orgData.length})</span>
            </div>
            <div className="right-box-content">
              {
                orgData.length > 0 && orgData.map((item, index) => {
                  return (
                    <div className="right-box-item" key={item.org_id}>
                      <div className="right-box-item-left">
                        <div>
                          {item.name}
                        </div>
                      </div>
                      <div className="icon" onClick={this.removeItem.bind(this, index, item.org_id)}>
                        <Icon type="close-circle" style={{ color: 'red' }} />
                      </div>
                    </div>
                  )
                })
              }

            </div>
          </div>

        </div>
        <div className="footer-btn">
          <Button type="primary" onClick={this.submitData.bind(this)}>确认</Button>
        </div>
      </Modal>
    );
  }
}

LeaderGroup.propTypes = {
  props: PropTypes.object,
  leaderGroup: PropTypes.object,
};
LeaderGroup.defaultProps = {
  props: {},
  leaderGroup: {},
};

const mapStateToProps = ({ leaderGroup }) => ({ leaderGroup });

export default connect(mapStateToProps)(Form.create()(LeaderGroup));
