import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Table,
  message,
} from "antd";
import {
  addPosition,
  deletePosition,
  getPositionList,
  queryByCode,
} from "client/apis/cadre-portrait";
import OrgTree from "client/components/org-tree";
import { useEffect, useRef, useState } from "react";
import "./index.less";
const { Option } = Select;
const FormItem = Form.Item;

const PositionManagement = ({ form, history }) => {
  console.log("🚀 ~ form:", form);
  const { getFieldDecorator, getFieldsValue, resetFields, setFieldsValue } =
    form;
  // 初始空 position 状态
  const initialPosition = {
    name: "",
    type: undefined,
    short_name: "",
    part_job_name: "",
    part_job_short_name: "",
    title: "",
    category: undefined,
    num: undefined,
    typeName: "", // 用于显示分类名称
  };

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pageState, setPageState] = useState({
    org_name: "",
    org_id: 1,
  });
  const [org_name, setOrgName] = useState("");
  const [org_ids, setOrgIds] = useState([1]);
  const [exportLoading, setExportLoading] = useState(false);
  const [position, setPosition] = useState(initialPosition);
  const [buttonStatus, setButtonStatus] = useState({
    save: false,
    modalSave: false,
  });
  const [codeMap, setCodeMap] = useState({
    categoryOption: [],
    classificationOption: [], //分类
  });
  const [visible, setVisible] = useState(false);
  const orgRef = useRef(null);
  useEffect(() => {
    initCodeMap(96160, "categoryOption");
    initCodeMap(96400, "classificationOption");
    initData(org_ids[0]);
  }, []);

  const initCodeMap = async (code, key) => {
    const res = await queryByCode({
      code,
    });
    if (res.data.code === 0) {
      codeMap[key] = res.data.data;
      setCodeMap({
        ...codeMap,
      });
    }
  };

  const initData = async (org_id) => {
    const fields = getFieldsValue();
    try {
      setLoading(true);
      const res = await getPositionList({
        org_id,
        name: fields.name,
        category: fields.category,
      });
      if (res.data.code === 0) {
        // 提取 pms_job_list 数据
        const pmsJobList = res.data.data.flatMap((item) => item.pms_job_list);
        setData(pmsJobList);
      } else {
        // 如果返回的 code 不为 0，说明有错误发生
        message.error(res.data.message || "获取数据失败");
      }
    } catch (error) {
      console.log("🚀 ~ error:", error);
      // 捕获网络请求或其他错误
      message.error("网络请求失败，请稍后再试");
    } finally {
      // 无论成功还是失败，最后都关闭加载状态
      setLoading(false);
    }
  };
  const onChange = (orgs, org) => {
    // setPage(1);
    setOrgIds(() => orgs);
    setOrgName(org.name);
    initData(orgs[0]);
  };

  const handleAdd = () => {
    setVisible(true);
    setPageState((state) => ({ ...state, page_status: 1 }));
    // 重置 position state 为初始空值
    setPosition(initialPosition);
    resetFields(); // 清除表单字段的值
  };

  const handleDelete = async (record) => {
    const res = await deletePosition({
      pms_job_id: record.pms_job_id,
    });
    if (res.data.code === 0) {
      message.success("删除成功");
      initData(pageState.org_id);
    } else {
      message.error(res.data.message);
    }
  };

  const handleEdit = (record) => {
    setVisible(true);
    setPageState((state) => ({ ...state, page_status: 2 }));

    // 查找分类名称
    const classificationOption =
      codeMap.classificationOption &&
      codeMap.classificationOption.find((item) => item.op_key == record.type);
    const typeName = classificationOption ? classificationOption.op_value : "";

    // 设置 position 的值
    setPosition({
      ...record,
      typeName: typeName, // 用于显示分类名称
    });

    // 设置表单的 type 字段为分类名称
    setFieldsValue({
      type: typeName,
    });
  };
  //操作按钮
  const onSearch = () => {
    setData([]); // 清空旧数据
    initData(pageState.org_id);
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };
  const onExport = async () => {
    // setExportLoading(true);
    // const params = getFieldsValue();
    // const res = await exportAnnualEval({
    //   ...params,
    //   org_id: pageState.org_id,
    // });
    // setExportLoading(false);
  };

  const onInputFile = () => {
    // history.push(
    //   `/import-page?type=2&org_id=${pageState.org_id}&org_name=${pageState.org_name}`
    // );
  };

  const handleModalOk = async () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      setButtonStatus((state) => ({ ...state, modalSave: true }));
      try {
        const { page_status } = pageState;
        let params = {
          ...values,
          organization_id: org_ids[0], // 添加当前选中的组织ID
        };
        if (page_status === 2) {
          // 编辑操作，添加唯一标识
          params = {
            ...params,
            pms_job_id: position.pms_job_id, // 假设 position 是当前编辑的记录对象
          };
        }

        // 如果是编辑状态，将分类名称转换回数字键
        if (page_status === 2) {
          const classificationOption =
            codeMap.classificationOption &&
            codeMap.classificationOption.find(
              (item) => item.op_value === values.type
            );
          if (classificationOption) {
            params.type = classificationOption.op_key; // 转换为数字键
          }
        }

        const res = await addPosition(params);

        if (res.data.code === 0) {
          message.success(`${page_status === 1 ? "新增" : "编辑"}成功`);
          initData(org_ids[0]); // 使用当前选中的组织ID刷新数据
        } else {
          message.error(res.data.message);
        }
      } catch (error) {
        // 网络请求错误处理
        message.error("请求失败，请稍后重试");
      } finally {
        // 无论成功还是失败都关闭加载状态
        setButtonStatus((state) => ({ ...state, modalSave: false }));
        setVisible(false);
      }
    });
  };

  const handleModalCancel = () => {
    setVisible(false);
    resetFields();
  };

  const formItemLayout = {
    labelCol: {
      span: 4,
    },
    wrapperCol: {
      span: 12,
    },
  };

  const columns = [
    {
      title: "职务全称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "职务简称",
      dataIndex: "short_name",
      key: "short_name",
    },
    {
      title: "兼职全称",
      dataIndex: "part_job_name",
      key: "part_job_name",
    },
    {
      title: "兼职简称",
      dataIndex: "part_job_short_name",
      key: "part_job_short_name",
    },
    {
      title: "发文抬头",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "职务分类",
      dataIndex: "type",
      key: "type",
      render: (type) => {
        const option = codeMap.classificationOption.find(
          (item) => item.op_key == type
        );
        return option ? option.op_value : "";
      },
    },
    {
      title: "职务数量",
      dataIndex: "num",
      key: "num",
    },
    {
      title: "干部姓名",
      dataIndex: "cadreName",
      key: "cadreName",
    },
    {
      title: "操作",
      key: "operation",
      render: (_, record) => (
        <div>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => handleDelete(record)}
            okText="是"
            cancelText="否"
          >
            <Button type="link" className="set_del">
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <div className="position-management">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} ref={(ref) => (orgRef.current = ref)} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="职务全称">
              {getFieldDecorator("name")(
                <Input
                  style={{ width: "200px" }}
                  placeholder="请输入"
                  allowClear
                />
              )}
            </Form.Item>

            <Form.Item label="职务类别">
              {getFieldDecorator("category")(
                <Select
                  style={{ minWidth: "200px" }}
                  placeholder="请选择"
                  allowClear
                >
                  {codeMap.categoryOption.map((item) => (
                    <Option key={item.op_key} value={item.op_key}>
                      {item.op_value}
                    </Option>
                  ))}
                </Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
              <Button
                onClick={onExport}
                loading={exportLoading}
                disabled={true}
                className="reset-btn"
              >
                导出
              </Button>
            </Form.Item>
          </Form>
        </div>
        <div className="add-box">
          <Button onClick={handleAdd} type="primary" icon="plus">
            添加职务
          </Button>
          <Button onClick={onInputFile} className="input-file" disabled={true}>
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={data}
          rowKey="pms_job_id"
        />
      </div>
      <Modal
        title={pageState.page_status === 1 ? "添加职务" : "修改职务"}
        visible={visible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        footer={[
          <Button key="cancel" onClick={handleModalCancel}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={buttonStatus.modalSave}
            onClick={handleModalOk}
          >
            保存
          </Button>,
        ]}
      >
        <Form {...formItemLayout}>
          {getFieldDecorator("pms_job_id")(<input type="hidden" />)}
          <FormItem label="分类">
            {getFieldDecorator("type", {
              initialValue: position.typeName || "", // 如果是编辑状态，回显分类名称
            })(
              <Select
                disabled={pageState.page_status === 2} // 编辑状态下禁用
                placeholder="请选择分类"
              >
                {codeMap.classificationOption.map((item) => (
                  <Option key={item.op_key} value={item.op_key}>
                    {item.op_value}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label="职务全称">
            {getFieldDecorator("name", {
              initialValue: position.name,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="职务简称">
            {getFieldDecorator("short_name", {
              initialValue: position.short_name,
              rules: [{ required: true, message: "请填写职务简称" }],
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="兼职全称">
            {getFieldDecorator("part_job_name", {
              initialValue: position.part_job_name,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="兼职简称">
            {getFieldDecorator("part_job_short_name", {
              initialValue: position.part_job_short_name,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="发文抬头">
            {getFieldDecorator("title", {
              initialValue: position.title,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="职务类别">
            {getFieldDecorator("category", {
              initialValue: position.category,
              rules: [{ required: true, message: "请选择职务类别" }],
            })(
              <Select>
                {codeMap.categoryOption.map((item) => (
                  <Option key={item.op_key} value={item.op_key}>
                    {item.op_value}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label="职务数量">
            {getFieldDecorator("num", {
              initialValue: position.num,
              rules: [{ required: true, message: "请填写职务数量" }],
            })(<InputNumber placeholder="请输入" />)}
          </FormItem>
        </Form>
      </Modal>
    </div>
  );
};

export default Form.create()(PositionManagement);
